using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NJS.Domain.Entities
{
    public class PlannedHoursMonth
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int WBSTaskId { get; set; }

        [Required]
        public int Year { get; set; }

        [Required]
        [Range(1, 12, ErrorMessage = "Month must be between 1 and 12")]
        public int Month { get; set; }

        [Required]
        [StringLength(20)]
        public string MonthName { get; set; } // e.g., "January", "February"

        [Required]
        [Range(0, 744, ErrorMessage = "Monthly hours must be between 0 and 744 (31*24)")]
        public double Hours { get; set; }

        public bool IsAggregated { get; set; } = false; // Tracks if this was auto-created from weekly aggregation

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [StringLength(100)]
        public string CreatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }

        [StringLength(100)]
        public string UpdatedBy { get; set; }

        // Navigation properties
        [ForeignKey(nameof(WBSTaskId))]
        public WBSTask WBSTask { get; set; }

        // Collection of weekly records that make up this month (if aggregated)
        public ICollection<PlannedHoursWeek> WeeklyRecords { get; set; } = new List<PlannedHoursWeek>();
    }
}
