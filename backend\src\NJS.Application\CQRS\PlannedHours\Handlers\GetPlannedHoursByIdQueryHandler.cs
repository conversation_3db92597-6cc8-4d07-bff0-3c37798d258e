using MediatR;
using Microsoft.Extensions.Logging;
using NJS.Application.CQRS.PlannedHours.Queries;
using NJS.Application.Dtos;
using NJS.Domain.Entities;
using NJS.Domain.Enums;
using NJS.Repositories.Interfaces;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace NJS.Application.CQRS.PlannedHours.Handlers
{
    public class GetPlannedHoursByIdQueryHandler : IRequestHandler<GetPlannedHoursByIdQuery, FrequencyResponseDto>
    {
        private readonly IPlannedHoursRepository _plannedHoursRepository;
        private readonly ILogger<GetPlannedHoursByIdQueryHandler> _logger;

        public GetPlannedHoursByIdQueryHandler(
            IPlannedHoursRepository plannedHoursRepository,
            ILogger<GetPlannedHoursByIdQueryHandler> logger)
        {
            _plannedHoursRepository = plannedHoursRepository ?? throw new ArgumentNullException(nameof(plannedHoursRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<FrequencyResponseDto> Handle(GetPlannedHoursByIdQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Getting planned hours by ID {Id}, frequency {FrequencyType}", 
                    request.Id, request.FrequencyType);

                var entity = await _plannedHoursRepository.GetByIdAsync(request.FrequencyType, request.Id);
                
                if (entity == null)
                {
                    _logger.LogWarning("Planned hours record with ID {Id} and frequency {FrequencyType} not found", 
                        request.Id, request.FrequencyType);
                    return null;
                }

                var response = MapToResponseDto(entity, request.FrequencyType);

                _logger.LogInformation("Successfully retrieved planned hours with ID {Id}", request.Id);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting planned hours by ID {Id}, frequency {FrequencyType}", 
                    request.Id, request.FrequencyType);
                throw;
            }
        }

        private FrequencyResponseDto MapToResponseDto(object entity, FrequencyType frequencyType)
        {
            return frequencyType switch
            {
                FrequencyType.Day => MapDayToDto((PlannedHoursDay)entity),
                FrequencyType.Week => MapWeekToDto((PlannedHoursWeek)entity),
                FrequencyType.Month => MapMonthToDto((PlannedHoursMonth)entity),
                _ => throw new ArgumentException($"Invalid frequency type: {frequencyType}")
            };
        }

        private FrequencyResponseDto MapDayToDto(PlannedHoursDay day)
        {
            return new FrequencyResponseDto
            {
                Id = day.Id,
                WBSTaskId = day.WBSTaskId,
                FrequencyType = FrequencyType.Day,
                Hours = day.Hours,
                IsAggregated = day.IsAggregated,
                CreatedAt = day.CreatedAt,
                CreatedBy = day.CreatedBy,
                UpdatedAt = day.UpdatedAt,
                UpdatedBy = day.UpdatedBy,
                Date = day.Date
            };
        }

        private FrequencyResponseDto MapWeekToDto(PlannedHoursWeek week)
        {
            return new FrequencyResponseDto
            {
                Id = week.Id,
                WBSTaskId = week.WBSTaskId,
                FrequencyType = FrequencyType.Week,
                Hours = week.Hours,
                IsAggregated = week.IsAggregated,
                CreatedAt = week.CreatedAt,
                CreatedBy = week.CreatedBy,
                UpdatedAt = week.UpdatedAt,
                UpdatedBy = week.UpdatedBy,
                WeekStartDate = week.WeekStartDate,
                WeekEndDate = week.WeekEndDate
            };
        }

        private FrequencyResponseDto MapMonthToDto(PlannedHoursMonth month)
        {
            return new FrequencyResponseDto
            {
                Id = month.Id,
                WBSTaskId = month.WBSTaskId,
                FrequencyType = FrequencyType.Month,
                Hours = month.Hours,
                IsAggregated = month.IsAggregated,
                CreatedAt = month.CreatedAt,
                CreatedBy = month.CreatedBy,
                UpdatedAt = month.UpdatedAt,
                UpdatedBy = month.UpdatedBy,
                Year = month.Year,
                Month = month.Month,
                MonthName = month.MonthName
            };
        }
    }
}
