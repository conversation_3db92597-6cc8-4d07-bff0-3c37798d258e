using MediatR;
using NJS.Application.Dtos;
using NJS.Domain.Enums;

namespace NJS.Application.CQRS.PlannedHours.Commands
{
    public record UpdatePlannedHoursCommand : IRequest<FrequencyResponseDto>
    {
        public int Id { get; init; }
        public FrequencyRequestDto Request { get; init; }

        public UpdatePlannedHoursCommand(int id, FrequencyRequestDto request)
        {
            Id = id;
            Request = request;
        }
    }
}
