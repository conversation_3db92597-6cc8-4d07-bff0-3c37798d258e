using Microsoft.Extensions.Logging;
using NJS.Application.Services.IContract;
using NJS.Domain.Entities;
using NJS.Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace NJS.Application.Services
{
    public class PlannedHoursAggregationService : IPlannedHoursAggregationService
    {
        private readonly IPlannedHoursRepository _plannedHoursRepository;
        private readonly ILogger<PlannedHoursAggregationService> _logger;

        public PlannedHoursAggregationService(
            IPlannedHoursRepository plannedHoursRepository,
            ILogger<PlannedHoursAggregationService> logger)
        {
            _plannedHoursRepository = plannedHoursRepository ?? throw new ArgumentNullException(nameof(plannedHoursRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<PlannedHoursWeek> TryAggregateDailyToWeeklyAsync(int wbsTaskId, DateTime weekStartDate, string createdBy)
        {
            try
            {
                _logger.LogInformation("Attempting to aggregate daily records to weekly for WBSTask {WBSTaskId}, week starting {WeekStartDate}", 
                    wbsTaskId, weekStartDate.ToString("yyyy-MM-dd"));

                // Ensure weekStartDate is actually a Monday
                weekStartDate = GetWeekStartDate(weekStartDate);
                var weekEndDate = GetWeekEndDate(weekStartDate);

                // Check if weekly record already exists
                if (await _plannedHoursRepository.WeekExistsAsync(wbsTaskId, weekStartDate))
                {
                    _logger.LogInformation("Weekly record already exists for WBSTask {WBSTaskId}, week starting {WeekStartDate}", 
                        wbsTaskId, weekStartDate.ToString("yyyy-MM-dd"));
                    return null;
                }

                // Get 7 consecutive days
                var dailyRecords = await _plannedHoursRepository.GetConsecutiveDaysForAggregationAsync(wbsTaskId, weekStartDate, 7);
                
                if (dailyRecords.Count() != 7)
                {
                    _logger.LogInformation("Cannot aggregate - only found {Count} consecutive daily records for WBSTask {WBSTaskId}, week starting {WeekStartDate}", 
                        dailyRecords.Count(), wbsTaskId, weekStartDate.ToString("yyyy-MM-dd"));
                    return null;
                }

                // Calculate total hours
                var totalHours = dailyRecords.Sum(d => d.Hours);

                // Create weekly record
                var weeklyRecord = new PlannedHoursWeek
                {
                    WBSTaskId = wbsTaskId,
                    WeekStartDate = weekStartDate,
                    WeekEndDate = weekEndDate,
                    Hours = totalHours,
                    IsAggregated = true,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = createdBy
                };

                var weeklyId = await _plannedHoursRepository.AddWeekAsync(weeklyRecord);
                weeklyRecord.Id = weeklyId;

                // Update daily records to mark them as aggregated
                foreach (var dailyRecord in dailyRecords)
                {
                    dailyRecord.IsAggregated = true;
                    dailyRecord.AggregatedFromWeekId = weeklyId;
                    dailyRecord.UpdatedAt = DateTime.UtcNow;
                    dailyRecord.UpdatedBy = createdBy;
                    await _plannedHoursRepository.UpdateDayAsync(dailyRecord);
                }

                _logger.LogInformation("Successfully aggregated {Count} daily records to weekly record {WeeklyId} for WBSTask {WBSTaskId}", 
                    dailyRecords.Count(), weeklyId, wbsTaskId);

                return weeklyRecord;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error aggregating daily records to weekly for WBSTask {WBSTaskId}, week starting {WeekStartDate}", 
                    wbsTaskId, weekStartDate.ToString("yyyy-MM-dd"));
                throw;
            }
        }

        public async Task<PlannedHoursMonth> TryAggregateWeeklyToMonthlyAsync(int wbsTaskId, int year, int month, string createdBy)
        {
            try
            {
                _logger.LogInformation("Attempting to aggregate weekly records to monthly for WBSTask {WBSTaskId}, {Year}-{Month:D2}", 
                    wbsTaskId, year, month);

                // Check if monthly record already exists
                if (await _plannedHoursRepository.MonthExistsAsync(wbsTaskId, year, month))
                {
                    _logger.LogInformation("Monthly record already exists for WBSTask {WBSTaskId}, {Year}-{Month:D2}", 
                        wbsTaskId, year, month);
                    return null;
                }

                // Get weekly records for the month
                var weeklyRecords = await _plannedHoursRepository.GetWeeksForMonthlyAggregationAsync(wbsTaskId, year, month);
                
                if (weeklyRecords.Count() < 4)
                {
                    _logger.LogInformation("Cannot aggregate - only found {Count} weekly records for WBSTask {WBSTaskId}, {Year}-{Month:D2}", 
                        weeklyRecords.Count(), wbsTaskId, year, month);
                    return null;
                }

                // Calculate total hours
                var totalHours = weeklyRecords.Sum(w => w.Hours);

                // Create monthly record
                var monthlyRecord = new PlannedHoursMonth
                {
                    WBSTaskId = wbsTaskId,
                    Year = year,
                    Month = month,
                    MonthName = GetMonthName(month),
                    Hours = totalHours,
                    IsAggregated = true,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = createdBy
                };

                var monthlyId = await _plannedHoursRepository.AddMonthAsync(monthlyRecord);
                monthlyRecord.Id = monthlyId;

                // Update weekly records to mark them as aggregated
                foreach (var weeklyRecord in weeklyRecords)
                {
                    weeklyRecord.IsAggregated = true;
                    weeklyRecord.AggregatedFromMonthId = monthlyId;
                    weeklyRecord.UpdatedAt = DateTime.UtcNow;
                    weeklyRecord.UpdatedBy = createdBy;
                    await _plannedHoursRepository.UpdateWeekAsync(weeklyRecord);
                }

                _logger.LogInformation("Successfully aggregated {Count} weekly records to monthly record {MonthlyId} for WBSTask {WBSTaskId}", 
                    weeklyRecords.Count(), monthlyId, wbsTaskId);

                return monthlyRecord;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error aggregating weekly records to monthly for WBSTask {WBSTaskId}, {Year}-{Month:D2}", 
                    wbsTaskId, year, month);
                throw;
            }
        }

        public async Task<bool> CanAggregateDailyToWeeklyAsync(int wbsTaskId, DateTime weekStartDate)
        {
            return await _plannedHoursRepository.CanAggregateToWeeklyAsync(wbsTaskId, GetWeekStartDate(weekStartDate));
        }

        public async Task<bool> CanAggregateWeeklyToMonthlyAsync(int wbsTaskId, int year, int month)
        {
            return await _plannedHoursRepository.CanAggregateToMonthlyAsync(wbsTaskId, year, month);
        }

        public async Task<List<object>> AutoAggregateAfterDailyChangeAsync(int wbsTaskId, DateTime date, string createdBy)
        {
            var aggregatedRecords = new List<object>();

            try
            {
                var weekStartDate = GetWeekStartDate(date);
                
                // Try to aggregate to weekly
                var weeklyRecord = await TryAggregateDailyToWeeklyAsync(wbsTaskId, weekStartDate, createdBy);
                if (weeklyRecord != null)
                {
                    aggregatedRecords.Add(weeklyRecord);

                    // If weekly aggregation succeeded, try monthly aggregation
                    var monthlyRecord = await AutoAggregateAfterWeeklyChangeAsync(wbsTaskId, weekStartDate, createdBy);
                    if (monthlyRecord != null)
                    {
                        aggregatedRecords.Add(monthlyRecord);
                    }
                }

                return aggregatedRecords;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in auto-aggregation after daily change for WBSTask {WBSTaskId}, date {Date}", 
                    wbsTaskId, date.ToString("yyyy-MM-dd"));
                throw;
            }
        }

        public async Task<PlannedHoursMonth> AutoAggregateAfterWeeklyChangeAsync(int wbsTaskId, DateTime weekStartDate, string createdBy)
        {
            try
            {
                var year = weekStartDate.Year;
                var month = weekStartDate.Month;

                return await TryAggregateWeeklyToMonthlyAsync(wbsTaskId, year, month, createdBy);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in auto-aggregation after weekly change for WBSTask {WBSTaskId}, week starting {WeekStartDate}", 
                    wbsTaskId, weekStartDate.ToString("yyyy-MM-dd"));
                throw;
            }
        }

        public DateTime GetWeekStartDate(DateTime date)
        {
            // Get the Monday of the week
            var daysFromMonday = (int)date.DayOfWeek - (int)DayOfWeek.Monday;
            if (daysFromMonday < 0) daysFromMonday += 7; // Handle Sunday
            return date.AddDays(-daysFromMonday).Date;
        }

        public DateTime GetWeekEndDate(DateTime date)
        {
            // Get the Sunday of the week
            var weekStart = GetWeekStartDate(date);
            return weekStart.AddDays(6);
        }

        public string GetMonthName(int month)
        {
            if (month < 1 || month > 12)
                throw new ArgumentOutOfRangeException(nameof(month), "Month must be between 1 and 12");

            return CultureInfo.InvariantCulture.DateTimeFormat.GetMonthName(month);
        }
    }
}
