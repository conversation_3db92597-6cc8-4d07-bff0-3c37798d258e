using NJS.Domain.Entities;
using NJS.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace NJS.Repositories.Interfaces
{
    public interface IPlannedHoursRepository
    {
        // Daily operations
        Task<PlannedHoursDay> GetDayByIdAsync(int id);
        Task<IEnumerable<PlannedHoursDay>> GetDaysByWBSTaskIdAsync(int wbsTaskId);
        Task<IEnumerable<PlannedHoursDay>> GetDaysByWBSTaskIdAndDateRangeAsync(int wbsTaskId, DateTime startDate, DateTime endDate);
        Task<PlannedHoursDay> GetDayByWBSTaskIdAndDateAsync(int wbsTaskId, DateTime date);
        Task<int> AddDayAsync(PlannedHoursDay plannedHoursDay);
        Task UpdateDayAsync(PlannedHoursDay plannedHoursDay);
        Task DeleteDayAsync(int id);
        Task<bool> DayExistsAsync(int wbsTaskId, DateTime date);

        // Weekly operations
        Task<PlannedHoursWeek> GetWeekByIdAsync(int id);
        Task<IEnumerable<PlannedHoursWeek>> GetWeeksByWBSTaskIdAsync(int wbsTaskId);
        Task<IEnumerable<PlannedHoursWeek>> GetWeeksByWBSTaskIdAndDateRangeAsync(int wbsTaskId, DateTime startDate, DateTime endDate);
        Task<PlannedHoursWeek> GetWeekByWBSTaskIdAndWeekStartAsync(int wbsTaskId, DateTime weekStartDate);
        Task<int> AddWeekAsync(PlannedHoursWeek plannedHoursWeek);
        Task UpdateWeekAsync(PlannedHoursWeek plannedHoursWeek);
        Task DeleteWeekAsync(int id);
        Task<bool> WeekExistsAsync(int wbsTaskId, DateTime weekStartDate);

        // Monthly operations
        Task<PlannedHoursMonth> GetMonthByIdAsync(int id);
        Task<IEnumerable<PlannedHoursMonth>> GetMonthsByWBSTaskIdAsync(int wbsTaskId);
        Task<IEnumerable<PlannedHoursMonth>> GetMonthsByWBSTaskIdAndYearAsync(int wbsTaskId, int year);
        Task<PlannedHoursMonth> GetMonthByWBSTaskIdYearAndMonthAsync(int wbsTaskId, int year, int month);
        Task<int> AddMonthAsync(PlannedHoursMonth plannedHoursMonth);
        Task UpdateMonthAsync(PlannedHoursMonth plannedHoursMonth);
        Task DeleteMonthAsync(int id);
        Task<bool> MonthExistsAsync(int wbsTaskId, int year, int month);

        // Aggregation support methods
        Task<IEnumerable<PlannedHoursDay>> GetConsecutiveDaysForAggregationAsync(int wbsTaskId, DateTime startDate, int dayCount = 7);
        Task<IEnumerable<PlannedHoursWeek>> GetWeeksForMonthlyAggregationAsync(int wbsTaskId, int year, int month);
        Task<bool> CanAggregateToWeeklyAsync(int wbsTaskId, DateTime weekStartDate);
        Task<bool> CanAggregateToMonthlyAsync(int wbsTaskId, int year, int month);

        // Generic operations by frequency type
        Task<object> GetByIdAsync(FrequencyType frequencyType, int id);
        Task<IEnumerable<object>> GetByWBSTaskIdAsync(FrequencyType frequencyType, int wbsTaskId);
        Task<int> AddAsync(FrequencyType frequencyType, object entity);
        Task UpdateAsync(FrequencyType frequencyType, object entity);
        Task DeleteAsync(FrequencyType frequencyType, int id);
    }
}
