using System;
using System.ComponentModel.DataAnnotations;

namespace NJS.Application.Dtos
{
    public class PlannedHoursMonthDto
    {
        public int Id { get; set; }

        [Required]
        public int WBSTaskId { get; set; }

        [Required]
        [Range(2020, 2050, ErrorMessage = "Year must be between 2020 and 2050")]
        public int Year { get; set; }

        [Required]
        [Range(1, 12, ErrorMessage = "Month must be between 1 and 12")]
        public int Month { get; set; }

        [Required]
        [StringLength(20)]
        public string MonthName { get; set; }

        [Required]
        [Range(0, 744, ErrorMessage = "Monthly hours must be between 0 and 744")]
        public double Hours { get; set; }

        public bool IsAggregated { get; set; } = false;

        public DateTime CreatedAt { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string? UpdatedBy { get; set; }
    }
}
