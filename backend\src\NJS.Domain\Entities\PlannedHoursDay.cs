using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NJS.Domain.Entities
{
    public class PlannedHoursDay
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int WBSTaskId { get; set; }

        [Required]
        public DateTime Date { get; set; }

        [Required]
        [Range(0, 24, ErrorMessage = "Hours must be between 0 and 24")]
        public double Hours { get; set; } = 8.0; // Default 8 hours per day

        public bool IsAggregated { get; set; } = false; // Tracks if this was auto-created from aggregation

        public int? AggregatedFromWeekId { get; set; } // Reference to week record if this was disaggregated

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [StringLength(100)]
        public string CreatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }

        [StringLength(100)]
        public string UpdatedBy { get; set; }

        // Navigation properties
        [ForeignKey(nameof(WBSTaskId))]
        public WBSTask WBSTask { get; set; }

        [ForeignKey(nameof(AggregatedFromWeekId))]
        public PlannedHoursWeek AggregatedFromWeek { get; set; }
    }
}
