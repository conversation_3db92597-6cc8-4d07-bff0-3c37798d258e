using MediatR;
using NJS.Application.Dtos;
using NJS.Domain.Enums;

namespace NJS.Application.CQRS.PlannedHours.Queries
{
    public record GetPlannedHoursByIdQuery : IRequest<FrequencyResponseDto>
    {
        public FrequencyType FrequencyType { get; init; }
        public int Id { get; init; }

        public GetPlannedHoursByIdQuery(FrequencyType frequencyType, int id)
        {
            FrequencyType = frequencyType;
            Id = id;
        }
    }
}
