using MediatR;
using NJS.Domain.Enums;

namespace NJS.Application.CQRS.PlannedHours.Commands
{
    public record DeletePlannedHoursCommand : IRequest<bool>
    {
        public FrequencyType FrequencyType { get; init; }
        public int Id { get; init; }

        public DeletePlannedHoursCommand(FrequencyType frequencyType, int id)
        {
            FrequencyType = frequencyType;
            Id = id;
        }
    }
}
