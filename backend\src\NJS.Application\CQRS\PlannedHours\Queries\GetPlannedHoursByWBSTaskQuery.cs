using MediatR;
using NJS.Application.Dtos;
using NJS.Domain.Enums;
using System.Collections.Generic;

namespace NJS.Application.CQRS.PlannedHours.Queries
{
    public record GetPlannedHoursByWBSTaskQuery : IRequest<IEnumerable<FrequencyResponseDto>>
    {
        public FrequencyType FrequencyType { get; init; }
        public int WBSTaskId { get; init; }

        public GetPlannedHoursByWBSTaskQuery(FrequencyType frequencyType, int wbsTaskId)
        {
            FrequencyType = frequencyType;
            WBSTaskId = wbsTaskId;
        }
    }
}
