using MediatR;
using Microsoft.Extensions.Logging;
using NJS.Application.CQRS.PlannedHours.Commands;
using NJS.Application.Dtos;
using NJS.Application.Services.IContract;
using NJS.Domain.Entities;
using NJS.Domain.Enums;
using NJS.Repositories.Interfaces;
using System;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;

namespace NJS.Application.CQRS.PlannedHours.Handlers
{
    public class UpdatePlannedHoursCommandHandler : IRequestHandler<UpdatePlannedHoursCommand, FrequencyResponseDto>
    {
        private readonly IPlannedHoursRepository _plannedHoursRepository;
        private readonly IPlannedHoursAggregationService _aggregationService;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<UpdatePlannedHoursCommandHandler> _logger;

        public UpdatePlannedHoursCommandHandler(
            IPlannedHoursRepository plannedHoursRepository,
            IPlannedHoursAggregationService aggregationService,
            ICurrentUserService currentUserService,
            ILogger<UpdatePlannedHoursCommandHandler> logger)
        {
            _plannedHoursRepository = plannedHoursRepository ?? throw new ArgumentNullException(nameof(plannedHoursRepository));
            _aggregationService = aggregationService ?? throw new ArgumentNullException(nameof(aggregationService));
            _currentUserService = currentUserService ?? throw new ArgumentNullException(nameof(currentUserService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<FrequencyResponseDto> Handle(UpdatePlannedHoursCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Updating planned hours with ID {Id}, frequency {FrequencyType}", 
                    request.Id, request.Request.FrequencyType);

                var currentUser = _currentUserService.UserId;
                var updatedEntity = await UpdateEntityByFrequencyType(request.Id, request.Request, currentUser);

                // Convert to response DTO
                var response = MapToResponseDto(updatedEntity, request.Request.FrequencyType);

                _logger.LogInformation("Successfully updated planned hours with ID {Id}", request.Id);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating planned hours with ID {Id}, frequency {FrequencyType}", 
                    request.Id, request.Request.FrequencyType);
                throw;
            }
        }

        private async Task<object> UpdateEntityByFrequencyType(int id, FrequencyRequestDto request, string currentUser)
        {
            var now = DateTime.UtcNow;

            switch (request.FrequencyType)
            {
                case FrequencyType.Day:
                    var dayEntity = await _plannedHoursRepository.GetDayByIdAsync(id);
                    if (dayEntity == null)
                        throw new InvalidOperationException($"Daily planned hours record with ID {id} not found");

                    // Prevent updating aggregated records
                    if (dayEntity.IsAggregated)
                        throw new InvalidOperationException("Cannot update aggregated daily records. Update the source weekly record instead.");

                    dayEntity.Hours = request.Hours;
                    dayEntity.UpdatedAt = now;
                    dayEntity.UpdatedBy = currentUser;

                    await _plannedHoursRepository.UpdateDayAsync(dayEntity);
                    return dayEntity;

                case FrequencyType.Week:
                    var weekEntity = await _plannedHoursRepository.GetWeekByIdAsync(id);
                    if (weekEntity == null)
                        throw new InvalidOperationException($"Weekly planned hours record with ID {id} not found");

                    // Prevent updating aggregated records
                    if (weekEntity.IsAggregated)
                        throw new InvalidOperationException("Cannot update aggregated weekly records. Update the source monthly record instead.");

                    weekEntity.Hours = request.Hours;
                    weekEntity.UpdatedAt = now;
                    weekEntity.UpdatedBy = currentUser;

                    await _plannedHoursRepository.UpdateWeekAsync(weekEntity);
                    return weekEntity;

                case FrequencyType.Month:
                    var monthEntity = await _plannedHoursRepository.GetMonthByIdAsync(id);
                    if (monthEntity == null)
                        throw new InvalidOperationException($"Monthly planned hours record with ID {id} not found");

                    monthEntity.Hours = request.Hours;
                    monthEntity.UpdatedAt = now;
                    monthEntity.UpdatedBy = currentUser;

                    await _plannedHoursRepository.UpdateMonthAsync(monthEntity);
                    return monthEntity;

                default:
                    throw new ArgumentException($"Invalid frequency type: {request.FrequencyType}");
            }
        }

        private FrequencyResponseDto MapToResponseDto(object entity, FrequencyType frequencyType)
        {
            return frequencyType switch
            {
                FrequencyType.Day => MapDayToDto((PlannedHoursDay)entity),
                FrequencyType.Week => MapWeekToDto((PlannedHoursWeek)entity),
                FrequencyType.Month => MapMonthToDto((PlannedHoursMonth)entity),
                _ => throw new ArgumentException($"Invalid frequency type: {frequencyType}")
            };
        }

        private FrequencyResponseDto MapDayToDto(PlannedHoursDay day)
        {
            return new FrequencyResponseDto
            {
                Id = day.Id,
                WBSTaskId = day.WBSTaskId,
                FrequencyType = FrequencyType.Day,
                Hours = day.Hours,
                IsAggregated = day.IsAggregated,
                CreatedAt = day.CreatedAt,
                CreatedBy = day.CreatedBy,
                UpdatedAt = day.UpdatedAt,
                UpdatedBy = day.UpdatedBy,
                Date = day.Date
            };
        }

        private FrequencyResponseDto MapWeekToDto(PlannedHoursWeek week)
        {
            return new FrequencyResponseDto
            {
                Id = week.Id,
                WBSTaskId = week.WBSTaskId,
                FrequencyType = FrequencyType.Week,
                Hours = week.Hours,
                IsAggregated = week.IsAggregated,
                CreatedAt = week.CreatedAt,
                CreatedBy = week.CreatedBy,
                UpdatedAt = week.UpdatedAt,
                UpdatedBy = week.UpdatedBy,
                WeekStartDate = week.WeekStartDate,
                WeekEndDate = week.WeekEndDate
            };
        }

        private FrequencyResponseDto MapMonthToDto(PlannedHoursMonth month)
        {
            return new FrequencyResponseDto
            {
                Id = month.Id,
                WBSTaskId = month.WBSTaskId,
                FrequencyType = FrequencyType.Month,
                Hours = month.Hours,
                IsAggregated = month.IsAggregated,
                CreatedAt = month.CreatedAt,
                CreatedBy = month.CreatedBy,
                UpdatedAt = month.UpdatedAt,
                UpdatedBy = month.UpdatedBy,
                Year = month.Year,
                Month = month.Month,
                MonthName = month.MonthName
            };
        }
    }
}
