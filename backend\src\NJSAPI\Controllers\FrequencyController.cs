using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using NJS.Application.CQRS.PlannedHours.Commands;
using NJS.Application.CQRS.PlannedHours.Queries;
using NJS.Application.Dtos;
using NJS.Domain.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;

namespace NJSAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class FrequencyController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<FrequencyController> _logger;

        public FrequencyController(IMediator mediator, ILogger<FrequencyController> logger)
        {
            _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Creates a new planned hours record for the specified frequency type
        /// </summary>
        /// <param name="frequencyType">The frequency type (Day, Week, Month)</param>
        /// <param name="request">The planned hours data</param>
        /// <returns>The created planned hours record</returns>
        [HttpPost("{frequencyType}")]
        [ProducesResponseType(typeof(FrequencyResponseDto), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status409Conflict)]
        public async Task<ActionResult<FrequencyResponseDto>> CreatePlannedHours(
            [FromRoute] FrequencyType frequencyType,
            [FromBody] FrequencyRequestDto request)
        {
            try
            {
                _logger.LogInformation("Creating planned hours for frequency {FrequencyType}", frequencyType);

                // Ensure the frequency type in the request matches the route
                request.FrequencyType = frequencyType;

                var command = new CreatePlannedHoursCommand(request);
                var result = await _mediator.Send(command);

                return CreatedAtAction(
                    nameof(GetPlannedHoursById),
                    new { frequencyType = frequencyType, id = result.Id },
                    result);
            }
            catch (ValidationException ex)
            {
                _logger.LogWarning("Validation error creating planned hours: {Message}", ex.Message);
                return BadRequest(new { error = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning("Conflict creating planned hours: {Message}", ex.Message);
                return Conflict(new { error = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating planned hours for frequency {FrequencyType}", frequencyType);
                return StatusCode(500, new { error = "An internal server error occurred" });
            }
        }

        /// <summary>
        /// Gets a planned hours record by ID and frequency type
        /// </summary>
        /// <param name="frequencyType">The frequency type (Day, Week, Month)</param>
        /// <param name="id">The record ID</param>
        /// <returns>The planned hours record</returns>
        [HttpGet("{frequencyType}/{id}")]
        [ProducesResponseType(typeof(FrequencyResponseDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<FrequencyResponseDto>> GetPlannedHoursById(
            [FromRoute] FrequencyType frequencyType,
            [FromRoute] int id)
        {
            try
            {
                _logger.LogInformation("Getting planned hours by ID {Id} for frequency {FrequencyType}", id, frequencyType);

                var query = new GetPlannedHoursByIdQuery(frequencyType, id);
                var result = await _mediator.Send(query);

                if (result == null)
                {
                    return NotFound(new { error = $"Planned hours record with ID {id} not found for frequency {frequencyType}" });
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting planned hours by ID {Id} for frequency {FrequencyType}", id, frequencyType);
                return StatusCode(500, new { error = "An internal server error occurred" });
            }
        }

        /// <summary>
        /// Gets all planned hours records for a WBS task by frequency type
        /// </summary>
        /// <param name="frequencyType">The frequency type (Day, Week, Month)</param>
        /// <param name="wbsTaskId">The WBS Task ID</param>
        /// <returns>List of planned hours records</returns>
        [HttpGet("{frequencyType}")]
        [ProducesResponseType(typeof(IEnumerable<FrequencyResponseDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<IEnumerable<FrequencyResponseDto>>> GetPlannedHoursByWBSTask(
            [FromRoute] FrequencyType frequencyType,
            [FromQuery] [Required] int wbsTaskId)
        {
            try
            {
                _logger.LogInformation("Getting planned hours for WBSTask {WBSTaskId} and frequency {FrequencyType}", wbsTaskId, frequencyType);

                var query = new GetPlannedHoursByWBSTaskQuery(frequencyType, wbsTaskId);
                var result = await _mediator.Send(query);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting planned hours for WBSTask {WBSTaskId} and frequency {FrequencyType}", wbsTaskId, frequencyType);
                return StatusCode(500, new { error = "An internal server error occurred" });
            }
        }

        /// <summary>
        /// Gets planned hours records for a WBS task within a date range
        /// </summary>
        /// <param name="frequencyType">The frequency type (Day, Week, Month)</param>
        /// <param name="wbsTaskId">The WBS Task ID</param>
        /// <param name="startDate">Start date for the range</param>
        /// <param name="endDate">End date for the range</param>
        /// <returns>List of planned hours records within the date range</returns>
        [HttpGet("{frequencyType}/range")]
        [ProducesResponseType(typeof(IEnumerable<FrequencyResponseDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<IEnumerable<FrequencyResponseDto>>> GetPlannedHoursByDateRange(
            [FromRoute] FrequencyType frequencyType,
            [FromQuery] [Required] int wbsTaskId,
            [FromQuery] [Required] DateTime startDate,
            [FromQuery] [Required] DateTime endDate)
        {
            try
            {
                _logger.LogInformation("Getting planned hours for WBSTask {WBSTaskId}, frequency {FrequencyType}, date range {StartDate} to {EndDate}", 
                    wbsTaskId, frequencyType, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));

                if (startDate > endDate)
                {
                    return BadRequest(new { error = "Start date cannot be greater than end date" });
                }

                var query = new GetPlannedHoursByDateRangeQuery(frequencyType, wbsTaskId, startDate, endDate);
                var result = await _mediator.Send(query);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting planned hours for WBSTask {WBSTaskId}, frequency {FrequencyType}, date range {StartDate} to {EndDate}", 
                    wbsTaskId, frequencyType, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));
                return StatusCode(500, new { error = "An internal server error occurred" });
            }
        }

        /// <summary>
        /// Updates a planned hours record
        /// </summary>
        /// <param name="frequencyType">The frequency type (Day, Week, Month)</param>
        /// <param name="id">The record ID</param>
        /// <param name="request">The updated planned hours data</param>
        /// <returns>The updated planned hours record</returns>
        [HttpPut("{frequencyType}/{id}")]
        [ProducesResponseType(typeof(FrequencyResponseDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<FrequencyResponseDto>> UpdatePlannedHours(
            [FromRoute] FrequencyType frequencyType,
            [FromRoute] int id,
            [FromBody] FrequencyRequestDto request)
        {
            try
            {
                _logger.LogInformation("Updating planned hours with ID {Id} for frequency {FrequencyType}", id, frequencyType);

                // Ensure the frequency type in the request matches the route
                request.FrequencyType = frequencyType;

                var command = new UpdatePlannedHoursCommand(id, request);
                var result = await _mediator.Send(command);

                return Ok(result);
            }
            catch (ValidationException ex)
            {
                _logger.LogWarning("Validation error updating planned hours: {Message}", ex.Message);
                return BadRequest(new { error = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning("Error updating planned hours: {Message}", ex.Message);
                return NotFound(new { error = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating planned hours with ID {Id} for frequency {FrequencyType}", id, frequencyType);
                return StatusCode(500, new { error = "An internal server error occurred" });
            }
        }

        /// <summary>
        /// Deletes a planned hours record
        /// </summary>
        /// <param name="frequencyType">The frequency type (Day, Week, Month)</param>
        /// <param name="id">The record ID</param>
        /// <returns>Success status</returns>
        [HttpDelete("{frequencyType}/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeletePlannedHours(
            [FromRoute] FrequencyType frequencyType,
            [FromRoute] int id)
        {
            try
            {
                _logger.LogInformation("Deleting planned hours with ID {Id} for frequency {FrequencyType}", id, frequencyType);

                var command = new DeletePlannedHoursCommand(frequencyType, id);
                var result = await _mediator.Send(command);

                if (result)
                {
                    return Ok(new { message = "Planned hours record deleted successfully" });
                }
                else
                {
                    return BadRequest(new { error = "Failed to delete planned hours record" });
                }
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning("Error deleting planned hours: {Message}", ex.Message);
                return BadRequest(new { error = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting planned hours with ID {Id} for frequency {FrequencyType}", id, frequencyType);
                return StatusCode(500, new { error = "An internal server error occurred" });
            }
        }
    }
}
