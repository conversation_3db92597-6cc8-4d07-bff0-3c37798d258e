﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NJS.Domain.Migrations
{
    /// <inheritdoc />
    public partial class AddFrequencyTablesFixed : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UserWBSTasks_AspNetUsers_UserId",
                table: "UserWBSTasks");

            migrationBuilder.DropForeignKey(
                name: "FK_WBSHistories_WBSTaskMonthlyHourHeader_WBSTaskMonthlyHourHeaderId",
                table: "WBSHistories");

            migrationBuilder.DropForeignKey(
                name: "FK_WBSTaskMonthlyHour_WBSTaskMonthlyHourHeader_WBSTaskMonthlyHourHeaderId",
                table: "WBSTaskMonthlyHour");

            migrationBuilder.DropForeignKey(
                name: "FK_WBSTaskMonthlyHour_WBSTasks_WBSTaskId",
                table: "WBSTaskMonthlyHour");

            migrationBuilder.DropForeignKey(
                name: "FK_WBSTaskMonthlyHourHeader_PMWorkflowStatuses_StatusId",
                table: "WBSTaskMonthlyHourHeader");

            migrationBuilder.DropForeignKey(
                name: "FK_WBSTaskMonthlyHourHeader_Projects_ProjectId",
                table: "WBSTaskMonthlyHourHeader");

            migrationBuilder.DropPrimaryKey(
                name: "PK_WBSTaskMonthlyHourHeader",
                table: "WBSTaskMonthlyHourHeader");

            migrationBuilder.DropPrimaryKey(
                name: "PK_WBSTaskMonthlyHour",
                table: "WBSTaskMonthlyHour");

            migrationBuilder.RenameTable(
                name: "WBSTaskMonthlyHourHeader",
                newName: "WBSTaskMonthlyHourHeaders");

            migrationBuilder.RenameTable(
                name: "WBSTaskMonthlyHour",
                newName: "WBSTaskMonthlyHours");

            migrationBuilder.RenameIndex(
                name: "IX_WBSTaskMonthlyHourHeader_StatusId",
                table: "WBSTaskMonthlyHourHeaders",
                newName: "IX_WBSTaskMonthlyHourHeaders_StatusId");

            migrationBuilder.RenameIndex(
                name: "IX_WBSTaskMonthlyHourHeader_ProjectId",
                table: "WBSTaskMonthlyHourHeaders",
                newName: "IX_WBSTaskMonthlyHourHeaders_ProjectId");

            migrationBuilder.RenameIndex(
                name: "IX_WBSTaskMonthlyHour_WBSTaskMonthlyHourHeaderId",
                table: "WBSTaskMonthlyHours",
                newName: "IX_WBSTaskMonthlyHours_WBSTaskMonthlyHourHeaderId");

            migrationBuilder.RenameIndex(
                name: "IX_WBSTaskMonthlyHour_WBSTaskId",
                table: "WBSTaskMonthlyHours",
                newName: "IX_WBSTaskMonthlyHours_WBSTaskId");

            migrationBuilder.AddColumn<string>(
                name: "ResourceRoleId",
                table: "UserWBSTasks",
                type: "nvarchar(450)",
                nullable: true);

            migrationBuilder.AddPrimaryKey(
                name: "PK_WBSTaskMonthlyHourHeaders",
                table: "WBSTaskMonthlyHourHeaders",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_WBSTaskMonthlyHours",
                table: "WBSTaskMonthlyHours",
                column: "Id");

            migrationBuilder.CreateTable(
                name: "JobStartFormHeaders",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    FormId = table.Column<int>(type: "int", nullable: false),
                    ProjectId = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StatusId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobStartFormHeaders", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobStartFormHeaders_JobStartForms_FormId",
                        column: x => x.FormId,
                        principalTable: "JobStartForms",
                        principalColumn: "FormId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_JobStartFormHeaders_PMWorkflowStatuses_StatusId",
                        column: x => x.StatusId,
                        principalTable: "PMWorkflowStatuses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_JobStartFormHeaders_Projects_ProjectId",
                        column: x => x.ProjectId,
                        principalTable: "Projects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "MonthlyProgresses",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ProjectId = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ManpowerTotal = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MonthlyProgresses", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MonthlyProgresses_Projects_ProjectId",
                        column: x => x.ProjectId,
                        principalTable: "Projects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PlannedHoursMonths",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    WBSTaskId = table.Column<int>(type: "int", nullable: false),
                    Year = table.Column<int>(type: "int", nullable: false),
                    Month = table.Column<int>(type: "int", nullable: false),
                    MonthName = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Hours = table.Column<double>(type: "float", nullable: false),
                    IsAggregated = table.Column<bool>(type: "bit", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PlannedHoursMonths", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PlannedHoursMonths_WBSTasks_WBSTaskId",
                        column: x => x.WBSTaskId,
                        principalTable: "WBSTasks",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "JobStartFormHistories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    JobStartFormHeaderId = table.Column<int>(type: "int", nullable: false),
                    StatusId = table.Column<int>(type: "int", nullable: false),
                    Action = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Comments = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ActionDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ActionBy = table.Column<string>(type: "nvarchar(450)", maxLength: 450, nullable: true),
                    AssignedToId = table.Column<string>(type: "nvarchar(450)", maxLength: 450, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobStartFormHistories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobStartFormHistories_AspNetUsers_ActionBy",
                        column: x => x.ActionBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_JobStartFormHistories_AspNetUsers_AssignedToId",
                        column: x => x.AssignedToId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_JobStartFormHistories_JobStartFormHeaders_JobStartFormHeaderId",
                        column: x => x.JobStartFormHeaderId,
                        principalTable: "JobStartFormHeaders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_JobStartFormHistories_PMWorkflowStatuses_StatusId",
                        column: x => x.StatusId,
                        principalTable: "PMWorkflowStatuses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "BudgetTables",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MonthlyProgressId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BudgetTables", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BudgetTables_MonthlyProgresses_MonthlyProgressId",
                        column: x => x.MonthlyProgressId,
                        principalTable: "MonthlyProgresses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ChangeOrders",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MonthlyProgressId = table.Column<int>(type: "int", nullable: false),
                    ContractTotal = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    Cost = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    Fee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    SummaryDetails = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Status = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ChangeOrders", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ChangeOrders_MonthlyProgresses_MonthlyProgressId",
                        column: x => x.MonthlyProgressId,
                        principalTable: "MonthlyProgresses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ContractAndCosts",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MonthlyProgressId = table.Column<int>(type: "int", nullable: false),
                    ContractType = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Percentage = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    ActualOdcs = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    ActualStaff = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    ActualSubtotal = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ContractAndCosts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ContractAndCosts_MonthlyProgresses_MonthlyProgressId",
                        column: x => x.MonthlyProgressId,
                        principalTable: "MonthlyProgresses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CTCEACs",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MonthlyProgressId = table.Column<int>(type: "int", nullable: false),
                    CtcODC = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    CtcStaff = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    CtcSubtotal = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    TotalEAC = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    GrossProfitPercentage = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CTCEACs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CTCEACs_MonthlyProgresses_MonthlyProgressId",
                        column: x => x.MonthlyProgressId,
                        principalTable: "MonthlyProgresses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CurrentMonthActions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MonthlyProgressId = table.Column<int>(type: "int", nullable: false),
                    CMactions = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CMAdate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CMAcomments = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CMApriority = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CurrentMonthActions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CurrentMonthActions_MonthlyProgresses_MonthlyProgressId",
                        column: x => x.MonthlyProgressId,
                        principalTable: "MonthlyProgresses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "EarlyWarnings",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MonthlyProgressId = table.Column<int>(type: "int", nullable: false),
                    WarningsDescription = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EarlyWarnings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_EarlyWarnings_MonthlyProgresses_MonthlyProgressId",
                        column: x => x.MonthlyProgressId,
                        principalTable: "MonthlyProgresses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "FinancialDetails",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MonthlyProgressId = table.Column<int>(type: "int", nullable: false),
                    Net = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    ServiceTax = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    FeeTotal = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    BudgetOdcs = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    BudgetStaff = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    BudgetSubTotal = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FinancialDetails", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FinancialDetails_MonthlyProgresses_MonthlyProgressId",
                        column: x => x.MonthlyProgressId,
                        principalTable: "MonthlyProgresses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "LastMonthActions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MonthlyProgressId = table.Column<int>(type: "int", nullable: false),
                    LMactions = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LMAdate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LMAcomments = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LastMonthActions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_LastMonthActions_MonthlyProgresses_MonthlyProgressId",
                        column: x => x.MonthlyProgressId,
                        principalTable: "MonthlyProgresses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ManpowerPlannings",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MonthlyProgressId = table.Column<int>(type: "int", nullable: false),
                    WorkAssignment = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Assignee = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Planned = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    Consumed = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    Balance = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    NextMonthPlanning = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    ManpowerComments = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ManpowerPlannings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ManpowerPlannings_MonthlyProgresses_MonthlyProgressId",
                        column: x => x.MonthlyProgressId,
                        principalTable: "MonthlyProgresses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ProgrammeSchedules",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MonthlyProgressId = table.Column<int>(type: "int", nullable: false),
                    ProgrammeDescription = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProgrammeSchedules", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ProgrammeSchedules_MonthlyProgresses_MonthlyProgressId",
                        column: x => x.MonthlyProgressId,
                        principalTable: "MonthlyProgresses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ProgressDeliverables",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MonthlyProgressId = table.Column<int>(type: "int", nullable: false),
                    Milestone = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DueDateContract = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DueDatePlanned = table.Column<DateTime>(type: "datetime2", nullable: false),
                    AchievedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    PaymentDue = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    InvoiceDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    PaymentReceivedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DeliverableComments = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProgressDeliverables", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ProgressDeliverables_MonthlyProgresses_MonthlyProgressId",
                        column: x => x.MonthlyProgressId,
                        principalTable: "MonthlyProgresses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Schedules",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MonthlyProgressId = table.Column<int>(type: "int", nullable: false),
                    DateOfIssueWOLOI = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CompletionDateAsPerContract = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CompletionDateAsPerExtension = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ExpectedCompletionDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Schedules", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Schedules_MonthlyProgresses_MonthlyProgressId",
                        column: x => x.MonthlyProgressId,
                        principalTable: "MonthlyProgresses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PlannedHoursWeeks",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    WBSTaskId = table.Column<int>(type: "int", nullable: false),
                    WeekStartDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    WeekEndDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Hours = table.Column<double>(type: "float", nullable: false),
                    IsAggregated = table.Column<bool>(type: "bit", nullable: false),
                    AggregatedFromMonthId = table.Column<int>(type: "int", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PlannedHoursWeeks", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PlannedHoursWeeks_PlannedHoursMonths_AggregatedFromMonthId",
                        column: x => x.AggregatedFromMonthId,
                        principalTable: "PlannedHoursMonths",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_PlannedHoursWeeks_WBSTasks_WBSTaskId",
                        column: x => x.WBSTaskId,
                        principalTable: "WBSTasks",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CurrentBudgetInMIS",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    BudgetTableId = table.Column<int>(type: "int", nullable: false),
                    RevenueFee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    Cost = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    ProfitPercentage = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CurrentBudgetInMIS", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CurrentBudgetInMIS_BudgetTables_BudgetTableId",
                        column: x => x.BudgetTableId,
                        principalTable: "BudgetTables",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "OriginalBudgets",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    BudgetTableId = table.Column<int>(type: "int", nullable: false),
                    RevenueFee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    Cost = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    ProfitPercentage = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OriginalBudgets", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OriginalBudgets_BudgetTables_BudgetTableId",
                        column: x => x.BudgetTableId,
                        principalTable: "BudgetTables",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PercentCompleteOnCosts",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    BudgetTableId = table.Column<int>(type: "int", nullable: false),
                    RevenueFee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    Cost = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PercentCompleteOnCosts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PercentCompleteOnCosts_BudgetTables_BudgetTableId",
                        column: x => x.BudgetTableId,
                        principalTable: "BudgetTables",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PlannedHoursDays",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    WBSTaskId = table.Column<int>(type: "int", nullable: false),
                    Date = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Hours = table.Column<double>(type: "float", nullable: false),
                    IsAggregated = table.Column<bool>(type: "bit", nullable: false),
                    AggregatedFromWeekId = table.Column<int>(type: "int", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PlannedHoursDays", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PlannedHoursDays_PlannedHoursWeeks_AggregatedFromWeekId",
                        column: x => x.AggregatedFromWeekId,
                        principalTable: "PlannedHoursWeeks",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_PlannedHoursDays_WBSTasks_WBSTaskId",
                        column: x => x.WBSTaskId,
                        principalTable: "WBSTasks",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_UserWBSTasks_ResourceRoleId",
                table: "UserWBSTasks",
                column: "ResourceRoleId");

            migrationBuilder.CreateIndex(
                name: "IX_BudgetTables_MonthlyProgressId",
                table: "BudgetTables",
                column: "MonthlyProgressId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ChangeOrders_MonthlyProgressId",
                table: "ChangeOrders",
                column: "MonthlyProgressId");

            migrationBuilder.CreateIndex(
                name: "IX_ContractAndCosts_MonthlyProgressId",
                table: "ContractAndCosts",
                column: "MonthlyProgressId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CTCEACs_MonthlyProgressId",
                table: "CTCEACs",
                column: "MonthlyProgressId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CurrentBudgetInMIS_BudgetTableId",
                table: "CurrentBudgetInMIS",
                column: "BudgetTableId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CurrentMonthActions_MonthlyProgressId",
                table: "CurrentMonthActions",
                column: "MonthlyProgressId");

            migrationBuilder.CreateIndex(
                name: "IX_EarlyWarnings_MonthlyProgressId",
                table: "EarlyWarnings",
                column: "MonthlyProgressId");

            migrationBuilder.CreateIndex(
                name: "IX_FinancialDetails_MonthlyProgressId",
                table: "FinancialDetails",
                column: "MonthlyProgressId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_JobStartFormHeaders_FormId",
                table: "JobStartFormHeaders",
                column: "FormId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_JobStartFormHeaders_ProjectId",
                table: "JobStartFormHeaders",
                column: "ProjectId");

            migrationBuilder.CreateIndex(
                name: "IX_JobStartFormHeaders_StatusId",
                table: "JobStartFormHeaders",
                column: "StatusId");

            migrationBuilder.CreateIndex(
                name: "IX_JobStartFormHistories_ActionBy",
                table: "JobStartFormHistories",
                column: "ActionBy");

            migrationBuilder.CreateIndex(
                name: "IX_JobStartFormHistories_AssignedToId",
                table: "JobStartFormHistories",
                column: "AssignedToId");

            migrationBuilder.CreateIndex(
                name: "IX_JobStartFormHistories_JobStartFormHeaderId",
                table: "JobStartFormHistories",
                column: "JobStartFormHeaderId");

            migrationBuilder.CreateIndex(
                name: "IX_JobStartFormHistories_StatusId",
                table: "JobStartFormHistories",
                column: "StatusId");

            migrationBuilder.CreateIndex(
                name: "IX_LastMonthActions_MonthlyProgressId",
                table: "LastMonthActions",
                column: "MonthlyProgressId");

            migrationBuilder.CreateIndex(
                name: "IX_ManpowerPlannings_MonthlyProgressId",
                table: "ManpowerPlannings",
                column: "MonthlyProgressId");

            migrationBuilder.CreateIndex(
                name: "IX_MonthlyProgresses_ProjectId",
                table: "MonthlyProgresses",
                column: "ProjectId");

            migrationBuilder.CreateIndex(
                name: "IX_OriginalBudgets_BudgetTableId",
                table: "OriginalBudgets",
                column: "BudgetTableId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PercentCompleteOnCosts_BudgetTableId",
                table: "PercentCompleteOnCosts",
                column: "BudgetTableId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PlannedHoursDays_AggregatedFromWeekId",
                table: "PlannedHoursDays",
                column: "AggregatedFromWeekId");

            migrationBuilder.CreateIndex(
                name: "IX_PlannedHoursDays_WBSTaskId_Date",
                table: "PlannedHoursDays",
                columns: new[] { "WBSTaskId", "Date" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PlannedHoursMonths_WBSTaskId_Year_Month",
                table: "PlannedHoursMonths",
                columns: new[] { "WBSTaskId", "Year", "Month" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PlannedHoursWeeks_AggregatedFromMonthId",
                table: "PlannedHoursWeeks",
                column: "AggregatedFromMonthId");

            migrationBuilder.CreateIndex(
                name: "IX_PlannedHoursWeeks_WBSTaskId_WeekStartDate",
                table: "PlannedHoursWeeks",
                columns: new[] { "WBSTaskId", "WeekStartDate" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ProgrammeSchedules_MonthlyProgressId",
                table: "ProgrammeSchedules",
                column: "MonthlyProgressId");

            migrationBuilder.CreateIndex(
                name: "IX_ProgressDeliverables_MonthlyProgressId",
                table: "ProgressDeliverables",
                column: "MonthlyProgressId");

            migrationBuilder.CreateIndex(
                name: "IX_Schedules_MonthlyProgressId",
                table: "Schedules",
                column: "MonthlyProgressId",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_UserWBSTasks_AspNetRoles_ResourceRoleId",
                table: "UserWBSTasks",
                column: "ResourceRoleId",
                principalTable: "AspNetRoles",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_UserWBSTasks_AspNetUsers_UserId",
                table: "UserWBSTasks",
                column: "UserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_WBSHistories_WBSTaskMonthlyHourHeaders_WBSTaskMonthlyHourHeaderId",
                table: "WBSHistories",
                column: "WBSTaskMonthlyHourHeaderId",
                principalTable: "WBSTaskMonthlyHourHeaders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_WBSTaskMonthlyHourHeaders_PMWorkflowStatuses_StatusId",
                table: "WBSTaskMonthlyHourHeaders",
                column: "StatusId",
                principalTable: "PMWorkflowStatuses",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_WBSTaskMonthlyHourHeaders_Projects_ProjectId",
                table: "WBSTaskMonthlyHourHeaders",
                column: "ProjectId",
                principalTable: "Projects",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_WBSTaskMonthlyHours_WBSTaskMonthlyHourHeaders_WBSTaskMonthlyHourHeaderId",
                table: "WBSTaskMonthlyHours",
                column: "WBSTaskMonthlyHourHeaderId",
                principalTable: "WBSTaskMonthlyHourHeaders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_WBSTaskMonthlyHours_WBSTasks_WBSTaskId",
                table: "WBSTaskMonthlyHours",
                column: "WBSTaskId",
                principalTable: "WBSTasks",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UserWBSTasks_AspNetRoles_ResourceRoleId",
                table: "UserWBSTasks");

            migrationBuilder.DropForeignKey(
                name: "FK_UserWBSTasks_AspNetUsers_UserId",
                table: "UserWBSTasks");

            migrationBuilder.DropForeignKey(
                name: "FK_WBSHistories_WBSTaskMonthlyHourHeaders_WBSTaskMonthlyHourHeaderId",
                table: "WBSHistories");

            migrationBuilder.DropForeignKey(
                name: "FK_WBSTaskMonthlyHourHeaders_PMWorkflowStatuses_StatusId",
                table: "WBSTaskMonthlyHourHeaders");

            migrationBuilder.DropForeignKey(
                name: "FK_WBSTaskMonthlyHourHeaders_Projects_ProjectId",
                table: "WBSTaskMonthlyHourHeaders");

            migrationBuilder.DropForeignKey(
                name: "FK_WBSTaskMonthlyHours_WBSTaskMonthlyHourHeaders_WBSTaskMonthlyHourHeaderId",
                table: "WBSTaskMonthlyHours");

            migrationBuilder.DropForeignKey(
                name: "FK_WBSTaskMonthlyHours_WBSTasks_WBSTaskId",
                table: "WBSTaskMonthlyHours");

            migrationBuilder.DropTable(
                name: "ChangeOrders");

            migrationBuilder.DropTable(
                name: "ContractAndCosts");

            migrationBuilder.DropTable(
                name: "CTCEACs");

            migrationBuilder.DropTable(
                name: "CurrentBudgetInMIS");

            migrationBuilder.DropTable(
                name: "CurrentMonthActions");

            migrationBuilder.DropTable(
                name: "EarlyWarnings");

            migrationBuilder.DropTable(
                name: "FinancialDetails");

            migrationBuilder.DropTable(
                name: "JobStartFormHistories");

            migrationBuilder.DropTable(
                name: "LastMonthActions");

            migrationBuilder.DropTable(
                name: "ManpowerPlannings");

            migrationBuilder.DropTable(
                name: "OriginalBudgets");

            migrationBuilder.DropTable(
                name: "PercentCompleteOnCosts");

            migrationBuilder.DropTable(
                name: "PlannedHoursDays");

            migrationBuilder.DropTable(
                name: "ProgrammeSchedules");

            migrationBuilder.DropTable(
                name: "ProgressDeliverables");

            migrationBuilder.DropTable(
                name: "Schedules");

            migrationBuilder.DropTable(
                name: "JobStartFormHeaders");

            migrationBuilder.DropTable(
                name: "BudgetTables");

            migrationBuilder.DropTable(
                name: "PlannedHoursWeeks");

            migrationBuilder.DropTable(
                name: "MonthlyProgresses");

            migrationBuilder.DropTable(
                name: "PlannedHoursMonths");

            migrationBuilder.DropIndex(
                name: "IX_UserWBSTasks_ResourceRoleId",
                table: "UserWBSTasks");

            migrationBuilder.DropPrimaryKey(
                name: "PK_WBSTaskMonthlyHours",
                table: "WBSTaskMonthlyHours");

            migrationBuilder.DropPrimaryKey(
                name: "PK_WBSTaskMonthlyHourHeaders",
                table: "WBSTaskMonthlyHourHeaders");

            migrationBuilder.DropColumn(
                name: "ResourceRoleId",
                table: "UserWBSTasks");

            migrationBuilder.RenameTable(
                name: "WBSTaskMonthlyHours",
                newName: "WBSTaskMonthlyHour");

            migrationBuilder.RenameTable(
                name: "WBSTaskMonthlyHourHeaders",
                newName: "WBSTaskMonthlyHourHeader");

            migrationBuilder.RenameIndex(
                name: "IX_WBSTaskMonthlyHours_WBSTaskMonthlyHourHeaderId",
                table: "WBSTaskMonthlyHour",
                newName: "IX_WBSTaskMonthlyHour_WBSTaskMonthlyHourHeaderId");

            migrationBuilder.RenameIndex(
                name: "IX_WBSTaskMonthlyHours_WBSTaskId",
                table: "WBSTaskMonthlyHour",
                newName: "IX_WBSTaskMonthlyHour_WBSTaskId");

            migrationBuilder.RenameIndex(
                name: "IX_WBSTaskMonthlyHourHeaders_StatusId",
                table: "WBSTaskMonthlyHourHeader",
                newName: "IX_WBSTaskMonthlyHourHeader_StatusId");

            migrationBuilder.RenameIndex(
                name: "IX_WBSTaskMonthlyHourHeaders_ProjectId",
                table: "WBSTaskMonthlyHourHeader",
                newName: "IX_WBSTaskMonthlyHourHeader_ProjectId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_WBSTaskMonthlyHour",
                table: "WBSTaskMonthlyHour",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_WBSTaskMonthlyHourHeader",
                table: "WBSTaskMonthlyHourHeader",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_UserWBSTasks_AspNetUsers_UserId",
                table: "UserWBSTasks",
                column: "UserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_WBSHistories_WBSTaskMonthlyHourHeader_WBSTaskMonthlyHourHeaderId",
                table: "WBSHistories",
                column: "WBSTaskMonthlyHourHeaderId",
                principalTable: "WBSTaskMonthlyHourHeader",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_WBSTaskMonthlyHour_WBSTaskMonthlyHourHeader_WBSTaskMonthlyHourHeaderId",
                table: "WBSTaskMonthlyHour",
                column: "WBSTaskMonthlyHourHeaderId",
                principalTable: "WBSTaskMonthlyHourHeader",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_WBSTaskMonthlyHour_WBSTasks_WBSTaskId",
                table: "WBSTaskMonthlyHour",
                column: "WBSTaskId",
                principalTable: "WBSTasks",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_WBSTaskMonthlyHourHeader_PMWorkflowStatuses_StatusId",
                table: "WBSTaskMonthlyHourHeader",
                column: "StatusId",
                principalTable: "PMWorkflowStatuses",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_WBSTaskMonthlyHourHeader_Projects_ProjectId",
                table: "WBSTaskMonthlyHourHeader",
                column: "ProjectId",
                principalTable: "Projects",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
