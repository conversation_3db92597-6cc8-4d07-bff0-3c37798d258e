using NJS.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace NJS.Application.Services.IContract
{
    public interface IPlannedHoursAggregationService
    {
        /// <summary>
        /// Attempts to aggregate 7 consecutive daily records into a weekly record
        /// </summary>
        /// <param name="wbsTaskId">The WBS Task ID</param>
        /// <param name="weekStartDate">The start date of the week (Monday)</param>
        /// <param name="createdBy">User who triggered the aggregation</param>
        /// <returns>The created weekly record if aggregation was successful, null otherwise</returns>
        Task<PlannedHoursWeek> TryAggregateDailyToWeeklyAsync(int wbsTaskId, DateTime weekStartDate, string createdBy);

        /// <summary>
        /// Attempts to aggregate 4 weekly records into a monthly record
        /// </summary>
        /// <param name="wbsTaskId">The WBS Task ID</param>
        /// <param name="year">The year</param>
        /// <param name="month">The month</param>
        /// <param name="createdBy">User who triggered the aggregation</param>
        /// <returns>The created monthly record if aggregation was successful, null otherwise</returns>
        Task<PlannedHoursMonth> TryAggregateWeeklyToMonthlyAsync(int wbsTaskId, int year, int month, string createdBy);

        /// <summary>
        /// Checks if daily records can be aggregated to weekly for a specific week
        /// </summary>
        /// <param name="wbsTaskId">The WBS Task ID</param>
        /// <param name="weekStartDate">The start date of the week</param>
        /// <returns>True if aggregation is possible</returns>
        Task<bool> CanAggregateDailyToWeeklyAsync(int wbsTaskId, DateTime weekStartDate);

        /// <summary>
        /// Checks if weekly records can be aggregated to monthly for a specific month
        /// </summary>
        /// <param name="wbsTaskId">The WBS Task ID</param>
        /// <param name="year">The year</param>
        /// <param name="month">The month</param>
        /// <returns>True if aggregation is possible</returns>
        Task<bool> CanAggregateWeeklyToMonthlyAsync(int wbsTaskId, int year, int month);

        /// <summary>
        /// Automatically triggers aggregation checks after a daily record is added/updated
        /// </summary>
        /// <param name="wbsTaskId">The WBS Task ID</param>
        /// <param name="date">The date of the daily record</param>
        /// <param name="createdBy">User who made the change</param>
        /// <returns>List of aggregated records (weekly and/or monthly)</returns>
        Task<List<object>> AutoAggregateAfterDailyChangeAsync(int wbsTaskId, DateTime date, string createdBy);

        /// <summary>
        /// Automatically triggers aggregation checks after a weekly record is added/updated
        /// </summary>
        /// <param name="wbsTaskId">The WBS Task ID</param>
        /// <param name="weekStartDate">The start date of the week</param>
        /// <param name="createdBy">User who made the change</param>
        /// <returns>Monthly record if aggregated, null otherwise</returns>
        Task<PlannedHoursMonth> AutoAggregateAfterWeeklyChangeAsync(int wbsTaskId, DateTime weekStartDate, string createdBy);

        /// <summary>
        /// Gets the Monday of the week for a given date
        /// </summary>
        /// <param name="date">Any date in the week</param>
        /// <returns>The Monday of that week</returns>
        DateTime GetWeekStartDate(DateTime date);

        /// <summary>
        /// Gets the Sunday of the week for a given date
        /// </summary>
        /// <param name="date">Any date in the week</param>
        /// <returns>The Sunday of that week</returns>
        DateTime GetWeekEndDate(DateTime date);

        /// <summary>
        /// Gets the month name for a given month number
        /// </summary>
        /// <param name="month">Month number (1-12)</param>
        /// <returns>Month name (e.g., "January")</returns>
        string GetMonthName(int month);
    }
}
