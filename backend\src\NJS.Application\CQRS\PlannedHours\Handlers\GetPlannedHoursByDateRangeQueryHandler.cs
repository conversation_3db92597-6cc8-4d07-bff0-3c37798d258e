using MediatR;
using Microsoft.Extensions.Logging;
using NJS.Application.CQRS.PlannedHours.Queries;
using NJS.Application.Dtos;
using NJS.Domain.Entities;
using NJS.Domain.Enums;
using NJS.Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace NJS.Application.CQRS.PlannedHours.Handlers
{
    public class GetPlannedHoursByDateRangeQueryHandler : IRequestHandler<GetPlannedHoursByDateRangeQuery, IEnumerable<FrequencyResponseDto>>
    {
        private readonly IPlannedHoursRepository _plannedHoursRepository;
        private readonly ILogger<GetPlannedHoursByDateRangeQueryHandler> _logger;

        public GetPlannedHoursByDateRangeQueryHandler(
            IPlannedHoursRepository plannedHoursRepository,
            ILogger<GetPlannedHoursByDateRangeQueryHandler> logger)
        {
            _plannedHoursRepository = plannedHoursRepository ?? throw new ArgumentNullException(nameof(plannedHoursRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<IEnumerable<FrequencyResponseDto>> Handle(GetPlannedHoursByDateRangeQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Getting planned hours for WBSTask {WBSTaskId}, frequency {FrequencyType}, date range {StartDate} to {EndDate}", 
                    request.WBSTaskId, request.FrequencyType, request.StartDate.ToString("yyyy-MM-dd"), request.EndDate.ToString("yyyy-MM-dd"));

                IEnumerable<object> entities;

                switch (request.FrequencyType)
                {
                    case FrequencyType.Day:
                        entities = await _plannedHoursRepository.GetDaysByWBSTaskIdAndDateRangeAsync(request.WBSTaskId, request.StartDate, request.EndDate);
                        break;
                    case FrequencyType.Week:
                        entities = await _plannedHoursRepository.GetWeeksByWBSTaskIdAndDateRangeAsync(request.WBSTaskId, request.StartDate, request.EndDate);
                        break;
                    case FrequencyType.Month:
                        // For monthly, we'll get all months for the years in the range
                        var startYear = request.StartDate.Year;
                        var endYear = request.EndDate.Year;
                        var allMonths = new List<PlannedHoursMonth>();
                        
                        for (int year = startYear; year <= endYear; year++)
                        {
                            var monthsForYear = await _plannedHoursRepository.GetMonthsByWBSTaskIdAndYearAsync(request.WBSTaskId, year);
                            allMonths.AddRange(monthsForYear);
                        }
                        
                        // Filter by the actual date range
                        entities = allMonths.Where(m => 
                        {
                            var monthStart = new DateTime(m.Year, m.Month, 1);
                            var monthEnd = monthStart.AddMonths(1).AddDays(-1);
                            return monthStart <= request.EndDate && monthEnd >= request.StartDate;
                        });
                        break;
                    default:
                        throw new ArgumentException($"Invalid frequency type: {request.FrequencyType}");
                }
                
                var response = entities.Select(entity => MapToResponseDto(entity, request.FrequencyType)).ToList();

                _logger.LogInformation("Successfully retrieved {Count} planned hours records for WBSTask {WBSTaskId} in date range", 
                    response.Count, request.WBSTaskId);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting planned hours for WBSTask {WBSTaskId}, frequency {FrequencyType}, date range {StartDate} to {EndDate}", 
                    request.WBSTaskId, request.FrequencyType, request.StartDate.ToString("yyyy-MM-dd"), request.EndDate.ToString("yyyy-MM-dd"));
                throw;
            }
        }

        private FrequencyResponseDto MapToResponseDto(object entity, FrequencyType frequencyType)
        {
            return frequencyType switch
            {
                FrequencyType.Day => MapDayToDto((PlannedHoursDay)entity),
                FrequencyType.Week => MapWeekToDto((PlannedHoursWeek)entity),
                FrequencyType.Month => MapMonthToDto((PlannedHoursMonth)entity),
                _ => throw new ArgumentException($"Invalid frequency type: {frequencyType}")
            };
        }

        private FrequencyResponseDto MapDayToDto(PlannedHoursDay day)
        {
            return new FrequencyResponseDto
            {
                Id = day.Id,
                WBSTaskId = day.WBSTaskId,
                FrequencyType = FrequencyType.Day,
                Hours = day.Hours,
                IsAggregated = day.IsAggregated,
                CreatedAt = day.CreatedAt,
                CreatedBy = day.CreatedBy,
                UpdatedAt = day.UpdatedAt,
                UpdatedBy = day.UpdatedBy,
                Date = day.Date
            };
        }

        private FrequencyResponseDto MapWeekToDto(PlannedHoursWeek week)
        {
            return new FrequencyResponseDto
            {
                Id = week.Id,
                WBSTaskId = week.WBSTaskId,
                FrequencyType = FrequencyType.Week,
                Hours = week.Hours,
                IsAggregated = week.IsAggregated,
                CreatedAt = week.CreatedAt,
                CreatedBy = week.CreatedBy,
                UpdatedAt = week.UpdatedAt,
                UpdatedBy = week.UpdatedBy,
                WeekStartDate = week.WeekStartDate,
                WeekEndDate = week.WeekEndDate
            };
        }

        private FrequencyResponseDto MapMonthToDto(PlannedHoursMonth month)
        {
            return new FrequencyResponseDto
            {
                Id = month.Id,
                WBSTaskId = month.WBSTaskId,
                FrequencyType = FrequencyType.Month,
                Hours = month.Hours,
                IsAggregated = month.IsAggregated,
                CreatedAt = month.CreatedAt,
                CreatedBy = month.CreatedBy,
                UpdatedAt = month.UpdatedAt,
                UpdatedBy = month.UpdatedBy,
                Year = month.Year,
                Month = month.Month,
                MonthName = month.MonthName
            };
        }
    }
}
