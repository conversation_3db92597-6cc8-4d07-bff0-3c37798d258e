using System;
using System.ComponentModel.DataAnnotations;

namespace NJS.Application.Dtos
{
    public class PlannedHoursDayDto
    {
        public int Id { get; set; }

        [Required]
        public int WBSTaskId { get; set; }

        [Required]
        public DateTime Date { get; set; }

        [Required]
        [Range(0, 24, ErrorMessage = "Hours must be between 0 and 24")]
        public double Hours { get; set; } = 8.0;

        public bool IsAggregated { get; set; } = false;

        public int? AggregatedFromWeekId { get; set; }

        public DateTime CreatedAt { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string? UpdatedBy { get; set; }
    }
}
