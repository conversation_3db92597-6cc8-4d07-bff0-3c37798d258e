using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NJS.Domain.Database;
using NJS.Domain.Entities;
using NJS.Domain.Enums;
using NJS.Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace NJS.Repositories.Repositories
{
    public class PlannedHoursRepository : IPlannedHoursRepository
    {
        private readonly ProjectManagementContext _context;
        private readonly ILogger<PlannedHoursRepository> _logger;

        public PlannedHoursRepository(ProjectManagementContext context, ILogger<PlannedHoursRepository> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #region Daily Operations

        public async Task<PlannedHoursDay> GetDayByIdAsync(int id)
        {
            return await _context.PlannedHoursDays
                .Include(phd => phd.WBSTask)
                .Include(phd => phd.AggregatedFromWeek)
                .FirstOrDefaultAsync(phd => phd.Id == id);
        }

        public async Task<IEnumerable<PlannedHoursDay>> GetDaysByWBSTaskIdAsync(int wbsTaskId)
        {
            return await _context.PlannedHoursDays
                .Where(phd => phd.WBSTaskId == wbsTaskId)
                .OrderBy(phd => phd.Date)
                .ToListAsync();
        }

        public async Task<IEnumerable<PlannedHoursDay>> GetDaysByWBSTaskIdAndDateRangeAsync(int wbsTaskId, DateTime startDate, DateTime endDate)
        {
            return await _context.PlannedHoursDays
                .Where(phd => phd.WBSTaskId == wbsTaskId && phd.Date >= startDate && phd.Date <= endDate)
                .OrderBy(phd => phd.Date)
                .ToListAsync();
        }

        public async Task<PlannedHoursDay> GetDayByWBSTaskIdAndDateAsync(int wbsTaskId, DateTime date)
        {
            return await _context.PlannedHoursDays
                .FirstOrDefaultAsync(phd => phd.WBSTaskId == wbsTaskId && phd.Date.Date == date.Date);
        }

        public async Task<int> AddDayAsync(PlannedHoursDay plannedHoursDay)
        {
            if (plannedHoursDay == null) throw new ArgumentNullException(nameof(plannedHoursDay));
            
            _context.PlannedHoursDays.Add(plannedHoursDay);
            await _context.SaveChangesAsync();
            return plannedHoursDay.Id;
        }

        public async Task UpdateDayAsync(PlannedHoursDay plannedHoursDay)
        {
            if (plannedHoursDay == null) throw new ArgumentNullException(nameof(plannedHoursDay));
            
            _context.PlannedHoursDays.Update(plannedHoursDay);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteDayAsync(int id)
        {
            var entity = await _context.PlannedHoursDays.FindAsync(id);
            if (entity != null)
            {
                _context.PlannedHoursDays.Remove(entity);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> DayExistsAsync(int wbsTaskId, DateTime date)
        {
            return await _context.PlannedHoursDays
                .AnyAsync(phd => phd.WBSTaskId == wbsTaskId && phd.Date.Date == date.Date);
        }

        #endregion

        #region Weekly Operations

        public async Task<PlannedHoursWeek> GetWeekByIdAsync(int id)
        {
            return await _context.PlannedHoursWeeks
                .Include(phw => phw.WBSTask)
                .Include(phw => phw.AggregatedFromMonth)
                .Include(phw => phw.DailyRecords)
                .FirstOrDefaultAsync(phw => phw.Id == id);
        }

        public async Task<IEnumerable<PlannedHoursWeek>> GetWeeksByWBSTaskIdAsync(int wbsTaskId)
        {
            return await _context.PlannedHoursWeeks
                .Where(phw => phw.WBSTaskId == wbsTaskId)
                .OrderBy(phw => phw.WeekStartDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<PlannedHoursWeek>> GetWeeksByWBSTaskIdAndDateRangeAsync(int wbsTaskId, DateTime startDate, DateTime endDate)
        {
            return await _context.PlannedHoursWeeks
                .Where(phw => phw.WBSTaskId == wbsTaskId && phw.WeekStartDate >= startDate && phw.WeekEndDate <= endDate)
                .OrderBy(phw => phw.WeekStartDate)
                .ToListAsync();
        }

        public async Task<PlannedHoursWeek> GetWeekByWBSTaskIdAndWeekStartAsync(int wbsTaskId, DateTime weekStartDate)
        {
            return await _context.PlannedHoursWeeks
                .FirstOrDefaultAsync(phw => phw.WBSTaskId == wbsTaskId && phw.WeekStartDate.Date == weekStartDate.Date);
        }

        public async Task<int> AddWeekAsync(PlannedHoursWeek plannedHoursWeek)
        {
            if (plannedHoursWeek == null) throw new ArgumentNullException(nameof(plannedHoursWeek));
            
            _context.PlannedHoursWeeks.Add(plannedHoursWeek);
            await _context.SaveChangesAsync();
            return plannedHoursWeek.Id;
        }

        public async Task UpdateWeekAsync(PlannedHoursWeek plannedHoursWeek)
        {
            if (plannedHoursWeek == null) throw new ArgumentNullException(nameof(plannedHoursWeek));
            
            _context.PlannedHoursWeeks.Update(plannedHoursWeek);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteWeekAsync(int id)
        {
            var entity = await _context.PlannedHoursWeeks.FindAsync(id);
            if (entity != null)
            {
                _context.PlannedHoursWeeks.Remove(entity);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> WeekExistsAsync(int wbsTaskId, DateTime weekStartDate)
        {
            return await _context.PlannedHoursWeeks
                .AnyAsync(phw => phw.WBSTaskId == wbsTaskId && phw.WeekStartDate.Date == weekStartDate.Date);
        }

        #endregion

        #region Monthly Operations

        public async Task<PlannedHoursMonth> GetMonthByIdAsync(int id)
        {
            return await _context.PlannedHoursMonths
                .Include(phm => phm.WBSTask)
                .Include(phm => phm.WeeklyRecords)
                .FirstOrDefaultAsync(phm => phm.Id == id);
        }

        public async Task<IEnumerable<PlannedHoursMonth>> GetMonthsByWBSTaskIdAsync(int wbsTaskId)
        {
            return await _context.PlannedHoursMonths
                .Where(phm => phm.WBSTaskId == wbsTaskId)
                .OrderBy(phm => phm.Year).ThenBy(phm => phm.Month)
                .ToListAsync();
        }

        public async Task<IEnumerable<PlannedHoursMonth>> GetMonthsByWBSTaskIdAndYearAsync(int wbsTaskId, int year)
        {
            return await _context.PlannedHoursMonths
                .Where(phm => phm.WBSTaskId == wbsTaskId && phm.Year == year)
                .OrderBy(phm => phm.Month)
                .ToListAsync();
        }

        public async Task<PlannedHoursMonth> GetMonthByWBSTaskIdYearAndMonthAsync(int wbsTaskId, int year, int month)
        {
            return await _context.PlannedHoursMonths
                .FirstOrDefaultAsync(phm => phm.WBSTaskId == wbsTaskId && phm.Year == year && phm.Month == month);
        }

        public async Task<int> AddMonthAsync(PlannedHoursMonth plannedHoursMonth)
        {
            if (plannedHoursMonth == null) throw new ArgumentNullException(nameof(plannedHoursMonth));
            
            _context.PlannedHoursMonths.Add(plannedHoursMonth);
            await _context.SaveChangesAsync();
            return plannedHoursMonth.Id;
        }

        public async Task UpdateMonthAsync(PlannedHoursMonth plannedHoursMonth)
        {
            if (plannedHoursMonth == null) throw new ArgumentNullException(nameof(plannedHoursMonth));
            
            _context.PlannedHoursMonths.Update(plannedHoursMonth);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteMonthAsync(int id)
        {
            var entity = await _context.PlannedHoursMonths.FindAsync(id);
            if (entity != null)
            {
                _context.PlannedHoursMonths.Remove(entity);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> MonthExistsAsync(int wbsTaskId, int year, int month)
        {
            return await _context.PlannedHoursMonths
                .AnyAsync(phm => phm.WBSTaskId == wbsTaskId && phm.Year == year && phm.Month == month);
        }

        #endregion

        #region Aggregation Support Methods

        public async Task<IEnumerable<PlannedHoursDay>> GetConsecutiveDaysForAggregationAsync(int wbsTaskId, DateTime startDate, int dayCount = 7)
        {
            var endDate = startDate.AddDays(dayCount - 1);
            var days = await _context.PlannedHoursDays
                .Where(phd => phd.WBSTaskId == wbsTaskId && 
                             phd.Date >= startDate && 
                             phd.Date <= endDate &&
                             !phd.IsAggregated)
                .OrderBy(phd => phd.Date)
                .ToListAsync();

            // Check if we have consecutive days
            if (days.Count == dayCount)
            {
                for (int i = 0; i < dayCount; i++)
                {
                    if (days[i].Date.Date != startDate.AddDays(i).Date)
                        return new List<PlannedHoursDay>(); // Not consecutive
                }
                return days;
            }

            return new List<PlannedHoursDay>();
        }

        public async Task<IEnumerable<PlannedHoursWeek>> GetWeeksForMonthlyAggregationAsync(int wbsTaskId, int year, int month)
        {
            var monthStart = new DateTime(year, month, 1);
            var monthEnd = monthStart.AddMonths(1).AddDays(-1);

            return await _context.PlannedHoursWeeks
                .Where(phw => phw.WBSTaskId == wbsTaskId &&
                             phw.WeekStartDate >= monthStart &&
                             phw.WeekEndDate <= monthEnd &&
                             !phw.IsAggregated)
                .OrderBy(phw => phw.WeekStartDate)
                .ToListAsync();
        }

        public async Task<bool> CanAggregateToWeeklyAsync(int wbsTaskId, DateTime weekStartDate)
        {
            var consecutiveDays = await GetConsecutiveDaysForAggregationAsync(wbsTaskId, weekStartDate, 7);
            return consecutiveDays.Count() == 7;
        }

        public async Task<bool> CanAggregateToMonthlyAsync(int wbsTaskId, int year, int month)
        {
            var weeks = await GetWeeksForMonthlyAggregationAsync(wbsTaskId, year, month);
            return weeks.Count() >= 4; // At least 4 weeks in a month
        }

        #endregion

        #region Generic Operations by Frequency Type

        public async Task<object> GetByIdAsync(FrequencyType frequencyType, int id)
        {
            return frequencyType switch
            {
                FrequencyType.Day => await GetDayByIdAsync(id),
                FrequencyType.Week => await GetWeekByIdAsync(id),
                FrequencyType.Month => await GetMonthByIdAsync(id),
                _ => throw new ArgumentException($"Invalid frequency type: {frequencyType}")
            };
        }

        public async Task<IEnumerable<object>> GetByWBSTaskIdAsync(FrequencyType frequencyType, int wbsTaskId)
        {
            return frequencyType switch
            {
                FrequencyType.Day => (await GetDaysByWBSTaskIdAsync(wbsTaskId)).Cast<object>(),
                FrequencyType.Week => (await GetWeeksByWBSTaskIdAsync(wbsTaskId)).Cast<object>(),
                FrequencyType.Month => (await GetMonthsByWBSTaskIdAsync(wbsTaskId)).Cast<object>(),
                _ => throw new ArgumentException($"Invalid frequency type: {frequencyType}")
            };
        }

        public async Task<int> AddAsync(FrequencyType frequencyType, object entity)
        {
            return frequencyType switch
            {
                FrequencyType.Day => await AddDayAsync((PlannedHoursDay)entity),
                FrequencyType.Week => await AddWeekAsync((PlannedHoursWeek)entity),
                FrequencyType.Month => await AddMonthAsync((PlannedHoursMonth)entity),
                _ => throw new ArgumentException($"Invalid frequency type: {frequencyType}")
            };
        }

        public async Task UpdateAsync(FrequencyType frequencyType, object entity)
        {
            switch (frequencyType)
            {
                case FrequencyType.Day:
                    await UpdateDayAsync((PlannedHoursDay)entity);
                    break;
                case FrequencyType.Week:
                    await UpdateWeekAsync((PlannedHoursWeek)entity);
                    break;
                case FrequencyType.Month:
                    await UpdateMonthAsync((PlannedHoursMonth)entity);
                    break;
                default:
                    throw new ArgumentException($"Invalid frequency type: {frequencyType}");
            }
        }

        public async Task DeleteAsync(FrequencyType frequencyType, int id)
        {
            switch (frequencyType)
            {
                case FrequencyType.Day:
                    await DeleteDayAsync(id);
                    break;
                case FrequencyType.Week:
                    await DeleteWeekAsync(id);
                    break;
                case FrequencyType.Month:
                    await DeleteMonthAsync(id);
                    break;
                default:
                    throw new ArgumentException($"Invalid frequency type: {frequencyType}");
            }
        }

        #endregion
    }
}
